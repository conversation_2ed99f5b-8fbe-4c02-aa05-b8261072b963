import * as actionTypes from './actionTypes';

export function digestSelectWorkspace(
  workspaceGuid,
  listPageAndBulkUpdateFeatureFlag
) {
  return {
    type: actionTypes.DIGEST_SELECT_WORKSPACE,
    payload: {
      workspaceGuid,
      listPageAndBulkUpdateFeatureFlag,
    },
  };
}

export function digestSelectWorkspaceForView(workspaceGuid, activeListView) {
  return {
    type: actionTypes.DIGEST_SELECT_WORKSPACE_FOR_VIEW,
    payload: {
      workspaceGuid,
      activeListView,
    },
  };
}

export function selectWorkspace(workspaceGuid, pageAlias) {
  return {
    type: actionTypes.SELECT_WORKSPACE,
    payload: {
      workspaceGuid,
      pageAlias,
    },
  };
}

export function loadWorkspace(
  workspaceGuid,
  workspaceChangeUUID,
  selectWorkspace
) {
  return {
    type: actionTypes.LOAD_WORKSPACE,
    payload: {
      workspaceGuid,
      selectWorkspace,
      workspaceChangeUUID,
    },
  };
}

export function loadWorkspaceSuccess(alias, payload, data) {
  return {
    type: actionTypes.LOAD_WORKSPACE_SUCCESSFUL,
    payload: {
      ...payload,
      data,
    },
  };
}

export function loadWorkspaces(
  groupByType,
  getWorkspaceDetail,
  selectedWorkspaceSurrogateId
) {
  return {
    type: actionTypes.LOAD_WORKSPACES,
    payload: {
      groupByType,
      getWorkspaceDetail,
      selectedWorkspaceSurrogateId,
    },
  };
}

export function digestLoadWorkspaces(
  payload,
  addDefaultMostRecentlyUsed,
  loadDefaultWorkspace,
  defaultWorkspaceGuid
) {
  return {
    type: actionTypes.DIGEST_LOAD_WORKSPACES,
    payload: {
      ...payload,
      addDefaultMostRecentlyUsed,
      loadDefaultWorkspace,
      defaultWorkspaceGuid,
    },
  };
}

export function validateSelectedWorkspaceOnInitLoad(payload) {
  return {
    type: actionTypes.VALIDATE_SELECTED_WORKSPACE_ON_INIT_LOAD,
    payload,
  };
}

export function digestLoadWorkspacesSuccess(alias, payload, data) {
  return {
    type: actionTypes.DIGEST_LOAD_WORKSPACES_SUCCESSFUL,
    payload: {
      ...payload,
      data,
    },
  };
}

export function loadWorkspacesSuccessSetup(payload) {
  return {
    type: actionTypes.LOAD_WORKSPACES_SUCCESSFUL_SETUP,
    payload: {
      ...payload,
    },
  };
}

export function loadWorkspacesSuccess(alias, payload, data) {
  return {
    type: actionTypes.LOAD_WORKSPACES_SUCCESSFUL,
    payload: {
      ...payload,
      data,
    },
  };
}

export function loadDefaultWorkspace(workspaceGuid, selectWorkspace) {
  return {
    type: actionTypes.LOAD_DEFAULT_WORKSPACE,
    payload: {
      workspaceGuid,
      selectWorkspace,
    },
  };
}

export function loadCopyWorkspaceTemplate(
  newWorkspaceAccessType,
  newWorkspaceEditRights,
  newWorkspaceTemplateGuid,
  doCreate,
  originalWorkspaceName,
  selectWorkspace
) {
  return {
    type: actionTypes.LOAD_COPY_WORKSPACE_TEMPLATE,
    payload: {
      newWorkspaceAccessType,
      newWorkspaceEditRights,
      newWorkspaceTemplateGuid,
      doCreate,
      originalWorkspaceName,
      workspaceGuid: newWorkspaceTemplateGuid,
      selectWorkspace,
    },
  };
}

export function loadCopyWorkspaceTemplateSuccess(alias, payload, data) {
  return {
    type: actionTypes.LOAD_COPY_WORKSPACE_TEMPLATE_SUCCESSFUL,
    payload: {
      ...payload,
      data,
    },
  };
}

export function deleteWorkspace(workspaceGuid) {
  return {
    type: actionTypes.DELETE_WORKSPACE,
    payload: {
      workspaceGuid,
    },
  };
}

export function deleteWorkspaceSuccess(alias, payload, data) {
  return {
    type: actionTypes.DELETE_WORKSPACE_SUCCESS,
    payload: {
      ...payload,
      data,
    },
  };
}

export function digestSetCreateWorkspaceChange(
  wsDescription,
  wsAccessType,
  wsEditRights,
  newWorkspaceTemplateGuid,
  doCreate,
  selectCreatedWorkspace
) {
  return {
    type: actionTypes.DIGEST_SET_CREATE_WORKSPACE_CHANGE,
    payload: {
      wsDescription,
      wsAccessType,
      wsEditRights,
      newWorkspaceTemplateGuid,
      doCreate,
      selectCreatedWorkspace,
    },
  };
}

export function digestSaveAsNewPlan(
  wsDescription,
  wsAccessType,
  wsEditRights,
  newWorkspaceTemplateGuid,
  workspaceColourthemeGuid,
  workspaceCustomColourTheme,
  doCreate,
  selectCreatedWorkspace
) {
  return {
    type: actionTypes.DIGEST_SAVE_AS_NEW_PLAN,
    payload: {
      wsDescription,
      wsAccessType,
      wsEditRights,
      newWorkspaceTemplateGuid,
      workspaceColourthemeGuid,
      workspaceCustomColourTheme,
      doCreate,
      selectCreatedWorkspace,
    },
  };
}
export function setCreateWorkspaceChange(
  newWSUUID,
  newWorkspaceStructure,
  newWorkspaceSettings,
  doCreate,
  selectCreatedWorkspace
) {
  return {
    type: actionTypes.SET_CREATE_WORKSPACE_CHANGE,
    payload: {
      newWSUUID,
      newWorkspaceStructure,
      newWorkspaceSettings,
      doCreate,
      selectCreatedWorkspace,
    },
  };
}

export function digestCreateWorkspace(
  wsAccessType,
  wsEditRights,
  newWorkspaceTemplateGuid,
  privatePlans,
  newPlanLabel,
  doCreate,
  selectCreatedWorkspace
) {
  return {
    type: actionTypes.DIGEST_CREATE_WORKSPACE,
    payload: {
      wsAccessType,
      wsEditRights,
      newWorkspaceTemplateGuid,
      privatePlans,
      newPlanLabel,
      doCreate,
      selectCreatedWorkspace,
    },
  };
}

export function digestCreateWorkspaceSuccess(alias, payload, data) {
  return {
    type: actionTypes.DIGEST_CREATE_WORKSPACE_SUCCESSFUL,
    payload: {
      ...payload,
      data,
    },
  };
}

export function createWorkspace(uuid, selectCreatedWorkspace) {
  return {
    type: actionTypes.CREATE_WORKSPACE,
    payload: {
      uuid,
      selectCreatedWorkspace,
    },
  };
}

export function doCreateWorkspace(
  data,
  createdWorkspaceChangeUUID,
  selectCreatedWorkspace
) {
  return {
    type: actionTypes.DO_CREATE_WORKSPACE,
    payload: {
      data,
      createdWorkspaceChangeUUID,
      selectCreatedWorkspace,
    },
  };
}

export function createWorkspaceSuccess(alias, payload, data) {
  return {
    type: actionTypes.CREATE_WORKSPACE_SUCCESSFUL,
    payload: {
      ...payload,
      data,
    },
  };
}

export function addMostRecentlyUsedWorkspace(workspaceGuid, workspace) {
  return {
    type: actionTypes.ADD_MOST_RECENTLY_USED_WORKSPACE,
    payload: {
      workspaceGuid,
      workspace,
    },
  };
}

export function addMostRecentlyUsedWorkspaceSuccess(alias, payload, data) {
  return {
    type: actionTypes.ADD_MOST_RECENTLY_USED_WORKSPACE_SUCCESSFUL,
    payload: {
      ...payload,
      data,
    },
  };
}

export function getMostRecentlyUsedWorkspaces(getWorkspaceDetail) {
  return {
    type: actionTypes.GET_MOST_RECENTLY_USED_WORKSPACES,
    payload: {
      getWorkspaceDetail,
    },
  };
}

export function getMostRecentlyUsedWorkspacesSuccess(alias, payload, data) {
  return {
    type: actionTypes.GET_MOST_RECENTLY_USED_WORKSPACES_SUCCESSFUL,
    payload: {
      ...payload,
      data,
    },
  };
}

export function dropMostRecentlyUsedWorkspace(workspaceGuid, workspaceData) {
  return {
    type: actionTypes.DROP_MOST_RECENTLY_USED_WORKSPACE,
    payload: {
      workspaceGuid,
      workspaceData,
    },
  };
}

export function dropMostRecentlyUsedWorkspaceSuccess(alias, payload, data) {
  return {
    type: actionTypes.DROP_MOST_RECENTLY_USED_WORKSPACE_SUCCESS,
    payload: {
      ...payload,
      data,
    },
  };
}

export function removeCreateWorkspaceChange(workspaceChangeUUID) {
  return {
    type: actionTypes.REMOVE_CREATE_WORKSPACE_CHANGE,
    payload: {
      workspaceChangeUUID,
    },
  };
}

export function digestCopyWorkspace(
  wsAccessType,
  wsEditRights,
  newWorkspaceTemplateGuid,
  doCreate,
  originalWorkspaceName,
  workspaceColourthemeGuid,
  workspaceCustomColourTheme
) {
  return {
    type: actionTypes.DIGEST_COPY_WORKSPACE,
    payload: {
      wsAccessType,
      wsEditRights,
      newWorkspaceTemplateGuid,
      doCreate,
      originalWorkspaceName,
      workspaceColourthemeGuid,
      workspaceCustomColourTheme,
    },
  };
}

export function digestCopyWorkspaceSuccess(alias, payload, data) {
  return {
    type: actionTypes.DIGEST_COPY_WORKSPACE_SUCCESSFUL,
    payload: {
      ...payload,
      data,
    },
  };
}

export function addFiltersModel(
  filtersAlias,
  contextGuid,
  title,
  filterGroups,
  selection,
  hidden
) {
  return {
    type: `${actionTypes.ADD_FILTERS_MODEL}_${filtersAlias}`,
    payload: {
      contextGuid,
      title,
      filterGroups,
      selection,
      hidden,
    },
  };
}

export function addMultipleViewsFiltersModel(
  { guid, selectedView, views },
  filtersConfig,
  pageFiltersAlias,
  hasHiddenFilters
) {
  return {
    type: `${actionTypes.FILTERS_ACTIONS.ADD_MULTIPLE_VIEWS_FILTERS_MODEL}_${pageFiltersAlias}`,
    payload: {
      guid,
      selectedView,
      views,
      filtersConfig,
      hasHiddenFilters,
    },
  };
}

export function setFilterState(
  filterState,
  pageFiltersAlias,
  hasHiddenFilters
) {
  return {
    type: `${actionTypes.FILTERS_ACTIONS.SET_FILTERS_STATE}_${pageFiltersAlias}`,
    payload: {
      filterState,
      hasHiddenFilters,
    },
  };
}

export function resetFilterToSavedPlan(filtersAlias, contextGuid, data) {
  return {
    type: `${actionTypes.FILTERS_ACTIONS.RESET_FILTER_TO_SAVED_PLAN}_${filtersAlias}`,
    payload: {
      contextGuid,
      data,
    },
  };
}

export const handleTableViewPageDataFilters = () => ({
  type: actionTypes.FILTERS_ACTIONS.HANDLE_TABLE_VIEW_PAGE_DATA_FILTERS,
});

export function populateSkillsValues(pageAlias) {
  return {
    type: `${actionTypes.FILTERS_ACTIONS.POPULATE_SKILLS}`,
    payload: {
      pageAlias,
    },
  };
}

export function setFiltersModel(alias, contextGuid, filtersModel) {
  return {
    type: `${actionTypes.SET_FILTERS_MODEL}_${alias}`,
    payload: {
      contextGuid,
      filtersModel,
    },
  };
}

export function addGrouppedTableDataModel(alias, guid, tableNames) {
  return {
    type: `${actionTypes.ADD_GROUPPED_TABLE_DATA_MODEL}_${alias}`,
    payload: {
      guid,
      tableNames,
    },
  };
}

export function addPagedDataModel(alias, guid, tableName, pageSize) {
  return {
    type: `${actionTypes.ADD_PAGED_DATA_MODEL}_${alias}`,
    payload: {
      guid,
      tableName,
      pageSize,
    },
  };
}

export function addDetailsPaneModel(guid, entityStructure, alias = '') {
  return {
    type: `${actionTypes.ADD_DETAILS_PANE_MODEL}_${alias}`,
    payload: {
      guid,
      entityStructure,
    },
  };
}

export function addPlannerDataModel(guid) {
  return {
    type: actionTypes.ADD_PLANNER_DATA_MODEL,
    payload: {
      guid,
    },
  };
}

export function filtersModelsLoadedSuccess(alias) {
  return {
    type: `${actionTypes.FILTERS_MODELS_LOADED_SUCCESS}_${alias}`,
  };
}

export function tableDataModelsLoadedSuccess(guid, tableName) {
  return {
    type: actionTypes.TABLE_DATA_MODELS_LOADED_SUCCESS,
    payload: {
      guid,
      tableName,
    },
  };
}

export function grouppedTableDataModelsLoadedSuccess(alias) {
  return {
    type: `${actionTypes.GROUPPED_TABLE_DATA_MODELS_LOADED_SUCCESS}_${alias}`,
  };
}

export function pagedDataModelsLoadedSuccess(alias, guid, tableName, pageSize) {
  return {
    type: `${actionTypes.PAGED_DATA_MODELS_LOADED_SUCCESS}_${alias}`,
    payload: {
      guid,
      tableName,
      pageSize,
    },
  };
}

export function detailsPaneModelsLoadedSuccess(guid, alias = '') {
  return {
    type: `${actionTypes.DETAILS_PANE_MODELS_LOADED_SUCCESS}_${alias}`,
    payload: {
      guid,
    },
  };
}

export function plannerDataModelsLoadedSuccess(guid) {
  return {
    type: actionTypes.PLANNER_DATA_MODELS_LOADED_SUCCESS,
    payload: {
      guid,
    },
  };
}

export function renameWorkspace(workspaceGuid, workspaceData) {
  return {
    type: actionTypes.RENAME_WORKSPACE,
    payload: {
      workspaceGuid,
      workspaceData,
    },
  };
}

export function renameWorkspaceSuccess(alias, payload, data) {
  return {
    type: actionTypes.RENAME_WORKSPACE_SUCCESS,
    payload: {
      ...payload,
      data,
    },
  };
}

export function moveWorkspace(workspaceGuid, workspaceData) {
  return {
    type: actionTypes.MOVE_WORKSPACE,
    payload: {
      workspaceGuid,
      workspaceData,
    },
  };
}

export function moveWorkspaceSuccess(alias, payload, data) {
  return {
    type: actionTypes.MOVE_WORKSPACE_SUCCESS,
    payload: {
      ...payload,
      data,
    },
  };
}

export function digestWorkspaceStructureChange(workspaceGuid, changes) {
  return {
    type: actionTypes.DIGEST_WORKSPACE_STRUCTURE_CHANGE,
    payload: {
      workspaceGuid,
      changes,
    },
  };
}

export function saveWorkspaceSettings(workspaceGuid, workspaceData) {
  return {
    type: actionTypes.SAVE_WORKSPACE_SETTINGS,
    payload: {
      workspaceGuid,
      workspaceData,
    },
  };
}

export function saveWorkspaceSettingsSuccess(alias, payload, data) {
  return {
    type: actionTypes.SAVE_WORKSPACE_SETTINGS_SUCCESSFUL,
    payload: {
      ...payload,
      data,
    },
  };
}

export function saveWorkspaceIfAnyChanges(
  saveWorkspaceGuid,
  selectWorkspaceGuid
) {
  return {
    type: actionTypes.SAVE_WORKSPACE_IF_ANY_CHANGES,
    payload: {
      saveWorkspaceGuid,
      selectWorkspaceGuid,
    },
  };
}

export function deleteWokspaceFilterSettings(workspaceGuid) {
  return {
    type: actionTypes.DELETE_WORKSPACE_FILTER_SETTINGS,
    payload: {
      workspaceGuid,
    },
  };
}
