import initialState from '../state/initialState';
import { LIST_PAGE_ACTIONS, SELECT_WORKSPACE } from '../actions/actionTypes';
import { TABLE_NAMES } from '../constants/globalConsts';
import workspacesReducer from './workspacesReducer';

export default (state = initialState.listPage, action) => {
  switch (action.type) {
    case LIST_PAGE_ACTIONS.UPDATE_LIST_VIEW: {
      return {
        ...state,
        activeListView: action.payload,
      };
    }
    case SELECT_WORKSPACE: {
      const { workspaceGuid, pageAlias } = action.payload;

      // Only handle workspace selection for listPage
      if (pageAlias !== 'listPage') {
        return state;
      }

      const activeView = state.activeListView;
      const workspaceKey =
        activeView === TABLE_NAMES.RESOURCE
          ? 'resourceWorkspaces'
          : 'jobWorkspaces';

      return {
        ...state,
        [workspaceKey]: workspacesReducer(state[workspaceKey], {
          type: SELECT_WORKSPACE,
          payload: { workspaceGuid, pageAlias },
        }),
      };
    }
    default: {
      return state;
    }
  }
};
