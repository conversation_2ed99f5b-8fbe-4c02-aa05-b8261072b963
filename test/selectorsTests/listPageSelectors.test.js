import {
    getActiveListView,
    getListPageJobWorkspaces,
    getListPageResourceWorkspaces,
    getActiveListViewWorkspaces,
    getContextualWorkspaces,
    getContextualSelectedWorkspaceGuid,
    getContextualSelectedWorkspaceSettings
} from "../../src/selectors/listPageSelectors";
import { TABLE_NAMES } from "../../src/constants/globalConsts";

describe("List Page Selectors tests", () => {
    const mockWorkspaces = {
        selected: 'workspace-1',
        workspacesStructure: {
            map: {
                'workspace-1': {
                    workspace_guid: 'workspace-1',
                    workspace_description: 'Test Workspace 1'
                }
            }
        },
        workspacesSettings: {
            map: {
                'workspace-1': {
                    workspace_guid: 'workspace-1',
                    masterRecTableName: 'job'
                }
            }
        }
    };

    const mockResourceWorkspaces = {
        selected: 'resource-workspace-1',
        workspacesStructure: {
            map: {
                'resource-workspace-1': {
                    workspace_guid: 'resource-workspace-1',
                    workspace_description: 'Test Resource Workspace 1'
                }
            }
        },
        workspacesSettings: {
            map: {
                'resource-workspace-1': {
                    workspace_guid: 'resource-workspace-1',
                    masterRecTableName: 'resource'
                }
            }
        }
    };

    const mockState = {
        listPage: {
            activeListView: 'job',
            jobWorkspaces: mockWorkspaces,
            resourceWorkspaces: mockResourceWorkspaces
        },
        plannerPage: {
            workspaces: mockWorkspaces
        }
    };

    describe("getActiveListView", () => {
        it("should return the active list view", () => {
            // Act
            const result = getActiveListView(mockState);

            // Assert
            expect(result).toEqual('job');
        });
    });

    describe("getListPageJobWorkspaces", () => {
        it("should return job workspaces", () => {
            // Act
            const result = getListPageJobWorkspaces(mockState);

            // Assert
            expect(result).toEqual(mockWorkspaces);
        });
    });

    describe("getListPageResourceWorkspaces", () => {
        it("should return resource workspaces", () => {
            // Act
            const result = getListPageResourceWorkspaces(mockState);

            // Assert
            expect(result).toEqual(mockResourceWorkspaces);
        });
    });

    describe("getActiveListViewWorkspaces", () => {
        it("should return job workspaces when active view is job", () => {
            // Act
            const result = getActiveListViewWorkspaces(mockState);

            // Assert
            expect(result).toEqual(mockWorkspaces);
        });

        it("should return resource workspaces when active view is resource", () => {
            // Arrange
            const stateWithResourceView = {
                ...mockState,
                listPage: {
                    ...mockState.listPage,
                    activeListView: TABLE_NAMES.RESOURCE
                }
            };

            // Act
            const result = getActiveListViewWorkspaces(stateWithResourceView);

            // Assert
            expect(result).toEqual(mockResourceWorkspaces);
        });
    });

    describe("getContextualWorkspaces", () => {
        it("should return planner page workspaces when not using list page context", () => {
            // Act
            const result = getContextualWorkspaces(mockState, false);

            // Assert
            expect(result).toEqual(mockWorkspaces);
        });

        it("should return job workspaces when using list page context with job view", () => {
            // Act
            const result = getContextualWorkspaces(mockState, true, 'job');

            // Assert
            expect(result).toEqual(mockWorkspaces);
        });

        it("should return resource workspaces when using list page context with resource view", () => {
            // Act
            const result = getContextualWorkspaces(mockState, true, TABLE_NAMES.RESOURCE);

            // Assert
            expect(result).toEqual(mockResourceWorkspaces);
        });
    });

    describe("getContextualSelectedWorkspaceGuid", () => {
        it("should return selected workspace guid from job workspaces", () => {
            // Act
            const result = getContextualSelectedWorkspaceGuid(mockState, true, 'job');

            // Assert
            expect(result).toEqual('workspace-1');
        });

        it("should return selected workspace guid from resource workspaces", () => {
            // Act
            const result = getContextualSelectedWorkspaceGuid(mockState, true, TABLE_NAMES.RESOURCE);

            // Assert
            expect(result).toEqual('resource-workspace-1');
        });
    });

    describe("getContextualSelectedWorkspaceSettings", () => {
        it("should return selected workspace settings from job workspaces", () => {
            // Act
            const result = getContextualSelectedWorkspaceSettings(mockState, true, 'job');

            // Assert
            expect(result).toEqual({
                workspace_guid: 'workspace-1',
                masterRecTableName: 'job'
            });
        });

        it("should return selected workspace settings from resource workspaces", () => {
            // Act
            const result = getContextualSelectedWorkspaceSettings(mockState, true, TABLE_NAMES.RESOURCE);

            // Assert
            expect(result).toEqual({
                workspace_guid: 'resource-workspace-1',
                masterRecTableName: 'resource'
            });
        });
    });
});
