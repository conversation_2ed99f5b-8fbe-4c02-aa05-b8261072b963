import { createSelector } from 'reselect';
import { TABLE_NAMES } from '../constants/globalConsts';

export const getActiveListView = createSelector(
  (state) => state.listPage,
  ({ activeListView }) => activeListView
);

export const getListPageJobWorkspaces = createSelector(
  (state) => state.listPage,
  ({ jobWorkspaces }) => jobWorkspaces
);

export const getListPageResourceWorkspaces = createSelector(
  (state) => state.listPage,
  ({ resourceWorkspaces }) => resourceWorkspaces
);

export const getActiveListViewWorkspaces = createSelector(
  getActiveListView,
  getListPageJobWorkspaces,
  getListPageResourceWorkspaces,
  (activeListView, jobWorkspaces, resourceWorkspaces) => {
    return activeListView === TABLE_NAMES.RESOURCE
      ? resourceWorkspaces
      : jobWorkspaces;
  }
);

// Wrapper selectors that work with both planner page and list page contexts
export const getContextualWorkspaces = (
  state,
  useListPageContext = false,
  activeListView = null
) => {
  if (useListPageContext) {
    if (activeListView === TABLE_NAMES.RESOURCE) {
      return getListPageResourceWorkspaces(state);
    } else {
      return getListPageJobWorkspaces(state);
    }
  }
  return state.plannerPage.workspaces;
};

export const getContextualSelectedWorkspaceGuid = createSelector(
  (state, useListPageContext, activeListView) =>
    getContextualWorkspaces(state, useListPageContext, activeListView),
  (workspaces) => workspaces.selected
);

export const getContextualSelectedWorkspaceSettings = createSelector(
  (state, useListPageContext, activeListView) =>
    getContextualWorkspaces(state, useListPageContext, activeListView),
  (workspaces) => {
    const workspacesSettings = (workspaces || {}).workspacesSettings;
    const selected = (workspaces || {}).selected;

    if (!(workspacesSettings || {}).hasOwnProperty('map')) {
      return {};
    }

    return workspacesSettings.map[selected];
  }
);
